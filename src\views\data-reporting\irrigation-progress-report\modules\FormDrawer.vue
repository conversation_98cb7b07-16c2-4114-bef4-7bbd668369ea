<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="formTitle" :loading="modalLoading" modalWidth="1400" modalHeight="728" @cancel="cancel"
     @ok="handleSave">
    <div slot="content">
      <div class="irrigation-progress-report">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-row">
            <div class="form-item">
              <label>灌溉时间：</label>
              <a-range-picker v-if="!currentRecord" v-model="formData.irrigationPeriod" :placeholder="['开始时间', '结束时间']"
                style="width: 300px" @change="onDateRangeChange" :disabledDate="disabledDate" />
              <span v-else class="readonly-value">
                {{ formData.irrigationPeriod && formData.irrigationPeriod.length === 2
                  ? `${formData.irrigationPeriod[0].format('YYYY-MM-DD')} ~
                ${formData.irrigationPeriod[1].format('YYYY-MM-DD')}`
                  : '-'
                }}
              </span>
            </div>
          </div>
        </div>

        <!-- 灌溉情况部分 -->
        <div class="irrigation-table-section">
          <h3 class="section-title">灌溉情况</h3>
          <vxe-table ref="irrigationTableRef" :key="tableKey" :data="tableData" border stripe
            :row-config="{ keyField: 'id' }" :column-config="{ resizable: true }"
            :edit-config="{ trigger: 'manual', mode: 'cell', showStatus: true }" height="500" class="irrigation-table"
            width="100%" :merge-cells="mergeCells">
            <!-- 单位列 -->
            <vxe-column field="unit" title="单位" width="120" align="center">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                  {{ row.unit }}
                </span>
              </template>
            </vxe-column>

            <!-- 直口实引水量列 -->
            <vxe-column field="waterVolume" title="直口实引水量（流量日）" width="260" align="center">
              <template #default="{ row }">
                <!-- 备注行使用单行输入框，占据整个合并单元格 -->
                <a-input v-if="row.unit === '备注'" v-model="formData.remark" style="width: 100%" placeholder="请输入备注信息"
                  @change="calculateTotalData" />
                <!-- 其他行使用数字输入框 -->
                <a-input-number v-else-if="!row.isTotal" v-model="row.waterVolume" :precision="2" :min="0"
                  style="width: 100%" @change="calculateTotalData" />
                <span v-else :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                  {{ row.unit === '备注' ? (formData.remark || '--') : (row.unit === '全灌域' ? (row.waterVolume || '0.00') :
                    (row.waterVolume || '-')) }}
                </span>
              </template>
            </vxe-column>

            <!-- 浇地面积列组 - 根据是否有农作物动态调整标题 -->
            <vxe-colgroup :title="getIrrigationAreaTitle()">
              <!-- 干地列 -->
              <vxe-column field="dryLand" title="干地" width="150" align="center">
                <template #default="{ row }">
                  <a-input v-if="row.unit === '备注'" v-model="row.dryLand" style="width: 100%" :disabled="row.isTotal"
                    @change="calculateTotalData" />
                  <a-input-number v-else-if="!row.isTotal" v-model="row.dryLand" :precision="2" :min="0"
                    style="width: 100%" @change="calculateTotalData" />
                  <span v-else :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                    {{ row.unit === '全灌域' ? (row.dryLand || '0.00') : (row.dryLand || '-') }}
                  </span>
                </template>
              </vxe-column>

              <!-- 热水地列 -->
              <vxe-column field="hotWaterLand" title="热水地" width="150" align="center">
                <template #default="{ row }">
                  <a-input v-if="row.unit === '备注'" v-model="row.hotWaterLand" style="width: 100%"
                    :disabled="row.isTotal" @change="calculateTotalData" />
                  <a-input-number v-else-if="!row.isTotal" v-model="row.hotWaterLand" :precision="2" :min="0"
                    style="width: 100%" @change="calculateTotalData" />
                  <span v-else :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                    {{ row.unit === '全灌域' ? (row.hotWaterLand || '0.00') : (row.hotWaterLand || '-') }}
                  </span>
                </template>
              </vxe-column>

              <!-- 动态生成农作物列 -->
              <vxe-column v-for="crop in cropColumns" :key="crop.code" :field="crop.code" :title="crop.name"
                :width="150" align="center">
                <template #default="{ row }">
                  <a-input v-if="row.unit === '备注'" v-model="row[crop.code]" style="width: 100%" :disabled="row.isTotal"
                    @change="calculateTotalData" />
                  <a-input-number v-else-if="!row.isTotal" v-model="row[crop.code]" :precision="2" :min="0"
                    style="width: 100%" @change="calculateTotalData" />
                  <span v-else :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                    {{ row.unit === '全灌域' ? (row[crop.code] || '0.00') : (row[crop.code] || '-') }}
                  </span>
                </template>
              </vxe-column>
            </vxe-colgroup>
          </vxe-table>
        </div>
      </div>
    </div>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'
import { getDeptTree, getIrrigationRound, addIrrigationSetting, updateIrrigationSetting, getIrrigationSettingById } from '../service'
import moment from 'moment'
export default {
  name: 'FormDrawer',
  props: [],
  components: { AntModal },
  data() {
    return {
      open: false,
      modalLoading: false,
      formTitle: '',
      formData: {
        irrigationPeriod: null, // 新增时默认为空
        remark: ''
      },
      tableData: [],
      cropColumns: [], // 农作物列配置
      deptList: [], // 部门列表
      currentRecord: null, // 当前编辑的记录
      irrigationRound: null, // 当前灌溉轮次
      tableKey: 0, // 用于强制重新渲染表格
      mergeCells: [] // 合并单元格配置
    }
  },
  methods: {
    // 禁用今天之后的日期
    disabledDate(current) {
      return current && current > moment().endOf('day')
    },

    // 动态生成浇地面积列组标题
    getIrrigationAreaTitle() {
      if (!this.cropColumns || this.cropColumns.length === 0) {
        // 没有农作物时，只显示基础标题
        return '浇地面积（万亩）'
      } else {
        // 有农作物时，显示完整标题
        return '浇地面积（万亩）'
      }
    },

    cancel() {
      this.open = false
      this.resetForm()
    },

    handleAdd() {
      this.open = true
      this.formTitle = '新增灌溉进度'
      this.currentRecord = null
      this.initData()
    },

    handleUpdate(record) {
      this.open = true
      this.formTitle = '编辑灌溉进度'
      this.currentRecord = record
      this.loadEditData(record)
    },

    // 重置表单
    resetForm() {
      this.formData.irrigationPeriod = null // 重置时设为空
      this.formData.remark = ''
      this.tableData = []
      this.cropColumns = []
      this.currentRecord = null
      this.irrigationRound = null
      this.tableKey = 0
      this.mergeCells = []
    },

    // 初始化数据
    async initData() {
      // 新增时设置默认灌溉时间为今天
      const today = moment()
      this.formData.irrigationPeriod = [today, today]
      this.formData.remark = ''
      this.cropColumns = [] // 初始时没有农作物列

      // 加载部门数据
      await this.loadDeptData()

      // 加载农作物列（基于默认的今天日期）
      await this.loadCropColumns(today.format('YYYY-MM-DD'), today.format('YYYY-MM-DD'))

      // 初始化表格数据
      this.initTableData()

      // 初始化后计算全灌域合计数据
      this.calculateTotalData()
    },

    // 加载编辑数据
    async loadEditData(record) {
      try {
        this.modalLoading = true
        const response = await getIrrigationSettingById(record.id)
        if (response.success) {
          const data = response.data
          this.formData.irrigationPeriod = [moment(data.startTime), moment(data.endTime)]

          // 加载农作物列
          await this.loadCropColumns(data.endTime, data.startTime)

          // 加载部门数据
          await this.loadDeptData()

          // 初始化表格数据并填充编辑数据
          this.initTableData()
          this.fillEditData(data)
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('加载编辑数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.modalLoading = false
      }
    },

    // 填充编辑数据
    fillEditData(data) {
      if (data.cstIrrigationProgressDetailDtoList) {
        data.cstIrrigationProgressDetailDtoList.forEach(detail => {
          let row

          // 特殊处理：depId为1的数据渲染到报总局行
          if (detail.depId === 0) {
            row = this.tableData.find(r => r.unit === '报总局')
          } else {
            row = this.tableData.find(r => r.deptId === detail.depId)
          }

          if (row) {
            row.waterVolume = detail.waterAmount
            row.dryLand = detail.dryLandPourAmount
            row.hotWaterLand = detail.hotWaterLandPourAmount

            // 先将所有农作物字段初始化为0.00
            this.cropColumns.forEach(crop => {
              row[crop.code] = 0.00
            })

            // 填充农作物数据
            if (detail.cropPourAreaDetail && detail.cropPourAreaDetail.length > 0) {
              detail.cropPourAreaDetail.forEach(crop => {
                row[crop.code] = crop.pourAmount
              })
            }
          }
        })
      }

      // 设置备注信息
      this.formData.remark = data.remark || ''

      // 计算全灌域数据
      this.calculateTotalData()
    },



    // 加载部门数据
    async loadDeptData() {
      try {
        const response = await getDeptTree()
        if (response.success && response.data && response.data.length > 0) {
          // 获取第一个children内的数组（各供水所）
          const firstLevel = response.data[0]
          if (firstLevel && firstLevel.children) {
            this.deptList = firstLevel.children
          }
        } else {
          this.$message.error('获取部门数据失败')
        }
      } catch (error) {
        console.error('加载部门数据失败:', error)
        this.$message.error('获取部门数据失败')
      }
    },

    // 日期区间变化事件
    async onDateRangeChange(dates) {
      this.formData.irrigationPeriod = dates
      if (dates && dates.length === 2) {
        const loadResult = await this.loadCropColumns(dates[1].format('YYYY-MM-DD'), dates[0].format('YYYY-MM-DD'))

        // 如果是新增模式且灌溉轮次为空，则提示用户并清空日期选择
        if (!this.currentRecord && !this.irrigationRound) {
          this.$message.warning('所选日期没有对应的灌溉轮次，无法新增灌溉进度')
          this.formData.irrigationPeriod = null
          this.$set(this, 'cropColumns', [])
          this.tableKey += 1
          this.initTableData()
          return
        }

        // 重新初始化表格数据以包含新的农作物列
        this.initTableData()
        // 重新计算全灌域合计数据
        this.calculateTotalData()
      } else {
        // 清空日期时，清空农作物列并重新初始化表格
        this.$set(this, 'cropColumns', [])
        this.tableKey += 1
        this.initTableData()
      }
    },

    // 加载农作物列
    async loadCropColumns(endDate, startDate) {
      try {
        const response = await getIrrigationRound(endDate, startDate)
        console.log('获取灌溉轮次响应:', response)
        if (response.success) {
          this.irrigationRound = response.data.irrigationRound

          // 先清空农作物列
          this.$set(this, 'cropColumns', [])

          // 直接使用接口返回的cropCodes和cropCodeNames
          if (response.data.cropCodes && response.data.cropCodeNames &&
            response.data.cropCodes.length === response.data.cropCodeNames.length &&
            response.data.cropCodes.length > 0) {
            const newCropColumns = []
            for (let i = 0; i < response.data.cropCodes.length; i++) {
              newCropColumns.push({
                code: response.data.cropCodes[i],
                name: response.data.cropCodeNames[i]
              })
            }
            // 使用Vue.set确保响应式更新
            this.$set(this, 'cropColumns', newCropColumns)
          } else {
            // 当没有农作物数据时，确保cropColumns为空数组
            console.log('没有农作物数据，清空农作物列')
          }

          // 无论是否有农作物，都要更新表格key以强制重新渲染
          this.tableKey += 1
          console.log('生成的农作物列:', this.cropColumns)
          console.log('当前灌溉轮次:', this.irrigationRound)

          return { success: true, irrigationRound: this.irrigationRound }
        } else {
          // 接口调用失败时也要清空农作物列和灌溉轮次
          this.irrigationRound = null
          this.$set(this, 'cropColumns', [])
          this.tableKey += 1
          this.$message.error(response.message || '获取农作物数据失败')
          return { success: false, irrigationRound: null }
        }
      } catch (error) {
        console.error('加载农作物数据失败:', error)
        // 异常时也要清空农作物列和灌溉轮次
        this.irrigationRound = null
        this.$set(this, 'cropColumns', [])
        this.tableKey += 1
        this.$message.error('获取农作物数据失败')
        return { success: false, irrigationRound: null }
      }
    },

    // 初始化表格数据
    initTableData() {
      console.log('初始化表格数据，当前农作物列:', this.cropColumns)
      this.tableData = []

      // 添加部门数据
      this.deptList.forEach((dept, index) => {
        const row = {
          id: index + 1,
          unit: dept.deptName,
          deptId: dept.deptId,
          waterVolume: 0.00,
          dryLand: 0.00,
          hotWaterLand: 0.00,
          isTotal: false
        }

        // 为每个农作物字段初始化数据
        this.cropColumns.forEach(crop => {
          row[crop.code] = 0.00
        })

        this.tableData.push(row)
      })

      // 添加固定行
      const fixedRows = [
        { name: '全灌域', isTotal: true },
        { name: '报总局', isTotal: false },
        { name: '备注', isTotal: false }
      ]

      fixedRows.forEach((fixedRow, index) => {
        const row = {
          id: this.deptList.length + index + 1,
          unit: fixedRow.name,
          deptId: null,
          waterVolume: fixedRow.name === '备注' ? null : 0.00,
          dryLand: fixedRow.name === '备注' ? null : 0.00,
          hotWaterLand: fixedRow.name === '备注' ? null : 0.00,
          isTotal: fixedRow.isTotal
        }

        // 为每个农作物字段初始化数据
        this.cropColumns.forEach(crop => {
          row[crop.code] = fixedRow.name === '备注' ? null : 0.00
        })

        this.tableData.push(row)
      })

      // 计算合并单元格配置
      this.calculateMergeCells()
    },

    // 计算全灌域合计数据
    calculateTotalData() {
      // 找到全灌域行
      const totalRow = this.tableData.find(row => row.unit === '全灌域')
      if (!totalRow) return

      // 全灌域只计算指定的四个供水所：南边渠供水所+北边渠供水所+合济渠供水所+永济渠供水所
      const targetDeptIds = [10021, 10022, 10035, 10070] // 南边渠、北边渠、合济渠、永济渠供水所
      const dataRows = this.tableData.filter(row =>
        row.deptId && targetDeptIds.includes(row.deptId)
      )

      // 计算各字段的合计
      const fields = ['waterVolume', 'dryLand', 'hotWaterLand', ...this.cropColumns.map(crop => crop.code)]

      fields.forEach(field => {
        let total = 0
        dataRows.forEach(row => {
          const value = parseFloat(row[field]) || 0
          total += value
        })
        // 始终显示为两位小数，包括0.00
        totalRow[field] = total.toFixed(2)
      })
    },

    // 计算合并单元格配置
    calculateMergeCells() {
      this.mergeCells = []

      // 找到备注行的索引
      const remarkRowIndex = this.tableData.findIndex(row => row.unit === '备注')
      if (remarkRowIndex !== -1) {
        // 计算需要合并的列数：直口实引水量 + 干地 + 热水地 + 农作物列数
        const mergeColspan = 1 + 1 + 1 + this.cropColumns.length // 1(直口实引水量) + 1(干地) + 1(热水地) + 农作物列数

        // 合并备注行从第二列开始的所有列
        this.mergeCells.push({
          row: remarkRowIndex,
          col: 1, // 从第二列开始（0是单位列，1是直口实引水量列）
          rowspan: 1,
          colspan: mergeColspan
        })
      }
    },

    // 保存
    async handleSave() {
      try {
        // 校验灌溉时间不能为空
        if (!this.formData.irrigationPeriod || this.formData.irrigationPeriod.length !== 2) {
          this.$message.error('请选择灌溉时间')
          return
        }

        // 校验灌溉时间的开始时间和结束时间都不能为空
        if (!this.formData.irrigationPeriod[0] || !this.formData.irrigationPeriod[1]) {
          this.$message.error('请选择完整的灌溉时间')
          return
        }

        // 新增模式下校验灌溉轮次不能为空
        if (!this.currentRecord && !this.irrigationRound) {
          this.$message.error('所选日期没有对应的灌溉轮次，无法新增灌溉进度')
          return
        }

        this.modalLoading = true

        // 构建保存数据
        const saveData = {
          startTime: this.formData.irrigationPeriod[0].format('YYYY-MM-DD'),
          endTime: this.formData.irrigationPeriod[1].format('YYYY-MM-DD'),
          cstIrrigationProgressDetailDtoList: [],
          remark: this.formData.remark
        }

        // 保存部门数据（不包括全灌域、备注）
        const deptRows = this.tableData.filter(row => (row.deptId && !row.isTotal) || row.unit === '报总局')

        deptRows.forEach(row => {
          const detail = {
            depId: row.unit === '报总局' ? 0 : row.deptId, // 报总局的depId固定为1
            waterAmount: parseFloat(row.waterVolume) || 0,
            dryLandPourAmount: parseFloat(row.dryLand) || 0,
            hotWaterLandPourAmount: parseFloat(row.hotWaterLand) || 0,
            cropPourAreaDetail: []
          }

          // 添加农作物数据
          this.cropColumns.forEach(crop => {
            const amount = parseFloat(row[crop.code]) || 0
            if (amount > 0) {
              detail.cropPourAreaDetail.push({
                code: crop.code,
                pourAmount: amount
              })
            }
          })

          saveData.cstIrrigationProgressDetailDtoList.push(detail)
        })

        // 调用保存接口
        let response
        if (this.currentRecord) {
          // 编辑模式
          saveData.id = this.currentRecord.id
          response = await updateIrrigationSetting(saveData)
        } else {
          // 新增模式
          response = await addIrrigationSetting(saveData)
        }

        if (response.success) {
          this.$message.success('保存成功')
          this.open = false
          this.resetForm()
          this.$emit('ok')
        } else {
          // this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.modalLoading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.irrigation-progress-report {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .form-row {
      display: flex;
      gap: 30px;

      .form-item {
        display: flex;
        align-items: center;

        label {
          margin-right: 8px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .irrigation-table-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .irrigation-table {
      width: 100%;

      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }

      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
      }

      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }

      /deep/ .vxe-table {
        width: 100% !important;
      }
    }
  }
}
</style>